import React from 'react';
import { SvgIcon, SvgIconProps } from '@mui/material';

interface DockToRightIconProps extends Omit<SvgIconProps, 'children'> {}

const DockToRightIcon: React.FC<DockToRightIconProps> = (props) => {
  return (
    <SvgIcon {...props} viewBox="0 0 24 24">
      {/* Dock to right icon - Material Design style */}
      <path d="M2 3v18h20V3H2zm18 16H4V5h16v14z"/>
      <path d="M16 6v12h4V6h-4zm2 10h-1V8h1v8z"/>
    </SvgIcon>
  );
};

export default DockToRightIcon;
