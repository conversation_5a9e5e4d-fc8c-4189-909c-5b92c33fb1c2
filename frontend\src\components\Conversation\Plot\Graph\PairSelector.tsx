import { useState, useEffect } from "react";
import {
  Box,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Typography,
  SelectChangeEvent,
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import useMediaQuery from '@mui/material/useMediaQuery';
import "./FunnelPlot.css";
import * as d3 from "d3";
import CustomTooltip from '../../../Common/CustomTooltip';

interface PairSelectorProps {
  citationIds: { key: string; value: string }[];
  messageId: string;
  plotData: any;
  onPairClicked: (pair: { intervention: string; outcome: string }) => void;
  currentInterventionId: string | null;
  currentOutcomeId: string | null;
}

const toSentenceCase = (str: string | undefined | null): string => {
  if (!str) return '';
  return String(str).split(';').map(segment => {
    const trimmedSegment = segment.trim();
    if (!trimmedSegment) return '';
    return trimmedSegment.charAt(0).toUpperCase() + trimmedSegment.slice(1).toLowerCase();
  }).join('; ');
};

const PairSelector = ({
  citationIds,
  messageId,
  plotData,
  onPairClicked,
  currentInterventionId,
  currentOutcomeId,
}: PairSelectorProps) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const [selectedIntervention, setSelectedIntervention] = useState<string | null>(
    currentInterventionId
  );
  const [selectedOutcome, setSelectedOutcome] = useState<string | null>(
    currentOutcomeId
  );

  const flatEffectSizes = plotData?.flat_effect_sizes || [];

  const outcomePairs = d3
    .groups(
      flatEffectSizes.filter(
        (d: any) =>
          String(d.intervention_tag_ids) === String(selectedIntervention)
      ),
      (d: any) => `${d.intervention_tag_ids}_${d.outcome_tag_ids}`
    )
    .sort((a, b) => b[1].length - a[1].length)
    .map(([pair, studies]) => ({
      interventionLabel: studies[0].intervention_tag_short_labels,
      outcomeLabel: studies[0].outcome_tag_short_labels,
      intervention_id: studies[0].intervention_tag_ids,
      outcome_id: studies[0].outcome_tag_ids,
      pair: pair,
      studies: studies,
      uniqueStudiesCount: Array.from(new Set(studies.map((s: any) => s.study_id)))
        .length,
      meanEffectSize: d3.mean(studies, (d) => d.hedges_d),
      meanLower: d3.mean(studies, (d) => d.standardized_ci_lower),
    }))
    .filter(d => (selectedIntervention === d.intervention_id && selectedOutcome === d.outcome_id) || (d.meanLower != null && d.meanLower > 0))
    .slice(0, 10);

  const interventionPairs = d3
    .groups(
      flatEffectSizes.filter(
        (d: any) => String(d.outcome_tag_ids) === String(selectedOutcome)
      ),
      (d: any) => `${d.intervention_tag_ids}_${d.outcome_tag_ids}`
    )
    .sort((a, b) => b[1].length - a[1].length)
    .map(([pair, studies]) => ({
      interventionLabel: studies[0].intervention_tag_short_labels,
      outcomeLabel: studies[0].outcome_tag_short_labels,
      intervention_id: studies[0].intervention_tag_ids,
      outcome_id: studies[0].outcome_tag_ids,
      pair: pair,
      studies: studies,
      uniqueStudiesCount: Array.from(new Set(studies.map((s: any) => s.study_id)))
        .length,
      meanEffectSize: d3.mean(studies, (d) => d.hedges_d),
      meanLower: d3.mean(studies, (d) => d.standardized_ci_lower),
    }))
    .filter(d => (selectedIntervention === d.intervention_id && selectedOutcome === d.outcome_id) || (d.meanLower != null && d.meanLower > 0))
    .slice(0, 10);

  const isSingleIntervention = interventionPairs.length === 1;
  const isSingleOutcome = outcomePairs.length === 1;

  const maxUpperLower = d3.max(flatEffectSizes, (d: any) =>
    Math.max(Math.abs(d.hedges_d), Math.abs(d.hedges_d))
  );

  const cScale = d3
    .scaleQuantize()
    .domain([-maxUpperLower, maxUpperLower])
    .range([
      "#67001f",
      "#b2182b",
      "#d6604d",
      "#f4a582",
      "#fddbc7",
      "#f7f7f7",
      "#d1e5f0",
      "#92c5de",
      "#4393c3",
      "#2166ac",
      "#053061",
    ]);

  useEffect(() => {
    setSelectedIntervention(currentInterventionId);
    setSelectedOutcome(currentOutcomeId);
  }, [currentInterventionId, currentOutcomeId, plotData]);

  const dropdownPaperStyles = {
    borderRadius: '4px',
    background: theme.palette.background.paperElevationZero,
    boxShadow: 8
  };

  const renderMenuItemContent = (pair: any, isIntervention: boolean) => (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        textOverflow: 'ellipsis',
        width: '100%',
      }}
    >
      <Box
        sx={{
          display: "inline-block",
          width: "12px",
          height: "12px",
          backgroundColor: cScale(pair.meanEffectSize || 0),
          borderRadius: "12px",
          border: "1px solid rgba(0, 51, 128, 0.7)",
          marginRight: "4px",
          transform: "translateY(2px)",
          flexShrink: 0,
        }}
      ></Box>
      <Typography component="span" sx={{
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        textOverflow: 'ellipsis',
        flexGrow: 1,
      }}>
        {toSentenceCase(isIntervention ? pair.interventionLabel : pair.outcomeLabel)}
      </Typography>
    </Box>
  );

  return (
    <Box
      sx={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        gap: "2px",
      }}
    >
      <Box
        sx={{
          display: "flex",
          flexDirection: isMobile ? "column" : "row",
          gap: isMobile ? "16px" : "8px",
          width: "100%",
        }}
      >
        <FormControl
          sx={{
            flex: isMobile ? '1 1 100%' : '1 1 50%',
            height: '40px',
            '& .MuiInputLabel-root': {
              lineHeight: '1',
              transform: 'translate(14px, 11px) scale(1)',
              '&.MuiInputLabel-shrink': {
                transform: 'translate(14px, -9px) scale(0.75)',
              },
            },
            '& .MuiOutlinedInput-root': {
              height: '40px',
              paddingTop: 0,
              paddingBottom: 0,
              '& .MuiOutlinedInput-input': {
                padding: '10px 14px',
              },
              '& fieldset': {
                borderColor: theme.palette.divider,
                borderWidth: '1px',
              },
              '&:hover fieldset': {
                borderColor: theme.components.input.outlined.hoverBorder,
                borderWidth: '1px',
              },
              '&.Mui-focused fieldset': {
                borderColor: theme.components.input.outlined.hoverBorder,
                borderWidth: '1px',
              },
              '& .MuiSelect-icon': {
                color: theme.palette.action.active,
              },
            },
          }}
        >
          <InputLabel id="intervention-select-label">Intervention</InputLabel>
          <Select
            labelId="intervention-select-label"
            id="intervention-select"
            value={selectedIntervention}
            label="Intervention"
            onChange={(e: SelectChangeEvent<string | null>) => {
              const interventionId = String(e.target.value);
              onPairClicked({
                intervention: interventionId,
                outcome: String(selectedOutcome),
              });
              setSelectedIntervention(interventionId);
            }}
            size="small"
            disabled={isSingleIntervention}
            IconComponent={isSingleIntervention ? () => null : undefined}
            MenuProps={{
              PaperProps: {
                sx: dropdownPaperStyles,
              },
            }}
          >
            {interventionPairs
              .sort((a, b) => (b.meanEffectSize || 0) - (a.meanEffectSize || 0))
              .map((pair) => (
                <MenuItem
                  key={pair.intervention_id}
                  value={pair.intervention_id}
                  sx={{
                    '&.MuiMenuItem-root:hover': {
                      backgroundColor: `${theme.palette.primary.hover}`,
                    },
                    '&.MuiMenuItem-root:active': {
                      backgroundColor: `${theme.palette.primary.selected}`,
                    },
                  }}
                >
                  {isSingleIntervention ? (
                    <CustomTooltip
                      content={toSentenceCase(pair.interventionLabel)}
                      placement="right"
                    >
                      {renderMenuItemContent(pair, true)}
                    </CustomTooltip>
                  ) : (
                    renderMenuItemContent(pair, true)
                  )}
                </MenuItem>
              ))}
          </Select>
        </FormControl>

        <FormControl
          sx={{
            flex: isMobile ? '1 1 100%' : '1 1 50%',
            height: '40px',
            '& .MuiInputLabel-root': {
              lineHeight: '1',
              transform: 'translate(14px, 11px) scale(1)',
              '&.MuiInputLabel-shrink': {
                transform: 'translate(14px, -9px) scale(0.75)',
              },
            },
            '& .MuiOutlinedInput-root': {
              height: '40px',
              paddingTop: 0,
              paddingBottom: 0,
              '& .MuiOutlinedInput-input': {
                padding: '10px 14px',
              },
              '& fieldset': {
                borderColor: theme.palette.divider,
                borderWidth: '1px',
              },
              '&:hover fieldset': {
                borderColor: theme.components.input.outlined.hoverBorder,
                borderWidth: '1px',
              },
              '&.Mui-focused fieldset': {
                borderColor: theme.components.input.outlined.hoverBorder,
                borderWidth: '1px',
              },
              '& .MuiSelect-icon': {
                color: theme.palette.action.active,
              },
            },
          }}
        >
          <InputLabel id="outcome-select-label">Outcome</InputLabel>
          <Select
            labelId="outcome-select-label"
            id="outcome-select"
            value={selectedOutcome}
            label="Outcome"
            onChange={(e: SelectChangeEvent<string | null>) => {
              const outcomeId = String(e.target.value);
              onPairClicked({
                intervention: String(selectedIntervention),
                outcome: outcomeId,
              });
              setSelectedOutcome(outcomeId);
            }}
            size="small"
            disabled={isSingleOutcome}
            IconComponent={isSingleOutcome ? () => null : undefined}
            MenuProps={{
              PaperProps: {
                sx: dropdownPaperStyles,
              },
            }}
          >
            {outcomePairs
              .sort((a, b) => (b.meanEffectSize || 0) - (a.meanEffectSize || 0))
              .map((pair) => (
                <MenuItem
                  key={pair.outcome_id}
                  value={pair.outcome_id}
                  sx={{
                    '&.MuiMenuItem-root:hover': {
                      backgroundColor: `${theme.palette.primary.hover}`,
                    },
                    '&.MuiMenuItem-root:active': {
                      backgroundColor: `${theme.palette.primary.selected}`,
                    },
                  }}
                >
                  {isSingleOutcome ? (
                    <CustomTooltip
                      content={toSentenceCase(pair.outcomeLabel)}
                      placement="right"
                    >
                      {renderMenuItemContent(pair, false)}
                    </CustomTooltip>
                  ) : (
                    renderMenuItemContent(pair, false)
                  )}
                </MenuItem>
              ))}
          </Select>
        </FormControl>
      </Box>
    </Box>
  );
};

export default PairSelector;