import React, { ReactNode, useState, useEffect, useCallback, useRef } from 'react';
import Tooltip, { TooltipProps } from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { useTheme } from '@mui/material/styles';
import Box from '@mui/material/Box';

interface CustomTooltipProps {
  children: ReactNode;
  content: ReactNode;
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'subtitle1' | 'subtitle2' | 'body1' | 'body2' | 'caption' | 'button' | 'overline' | 'inherit';
  fontSize?: string | number;
  disableOnScroll?: boolean;
  scrollContainer?: string;
  placement?: TooltipProps['placement'];
  arrow?: boolean;
  PopperProps?: TooltipProps['PopperProps'];
}

const CustomTooltip: React.FC<CustomTooltipProps> = ({
  children,
  content,
  variant = 'caption',
  fontSize = '12px',
  disableOnScroll = false,
  scrollContainer = 'sidenav-infinite-scroll',
  placement = 'right',
  arrow = false,
  PopperProps,
  ...rest
}) => {
  const theme = useTheme();
  const [open, setOpen] = useState(false);
  const scrollTimeoutRef = useRef<number | null>(null);
  const isScrollingRef = useRef(false);

  const SCROLL_DEBOUNCE_DELAY = 50;

  const handleScroll = useCallback(() => {
    if (!disableOnScroll) return;

    isScrollingRef.current = true;
    setOpen(false);

    if (scrollTimeoutRef.current) {
      window.clearTimeout(scrollTimeoutRef.current);
    }

    scrollTimeoutRef.current = window.setTimeout(() => {
      isScrollingRef.current = false;
    }, SCROLL_DEBOUNCE_DELAY);
  }, [disableOnScroll]);

  useEffect(() => {
    if (!disableOnScroll) return;

    const scrollElement = document.getElementById(scrollContainer);
    if (scrollElement) {
      scrollElement.addEventListener('scroll', handleScroll);

      return () => {
        scrollElement.removeEventListener('scroll', handleScroll);
        if (scrollTimeoutRef.current) {
          window.clearTimeout(scrollTimeoutRef.current);
        }
      };
    }
  }, [disableOnScroll, scrollContainer, handleScroll]);

  const handleTooltipClose = () => {
    setOpen(false);
  };

  const handleTooltipOpen = () => {
    if (!isScrollingRef.current) {
      setOpen(true);
    }
  };

  const renderChildren = () => {
    if (React.isValidElement(children)) {
      return React.cloneElement(children);
    }
    return <Box component="span">{children}</Box>;
  };

  return (
    <Tooltip
      {...rest}
      open={open}
      onClose={handleTooltipClose}
      onOpen={handleTooltipOpen}
      placement={placement}
      arrow={arrow}
      PopperProps={{
        sx: {
          zIndex: theme.zIndex.tooltip + 100,
          ...PopperProps?.sx
        },
        ...PopperProps
      }}
      slotProps={{
        tooltip: {
          sx: {
            borderRadius: '4px',
            backgroundColor: theme.palette.grey[800],
            border: 'none',
            maxWidth: 200,
          },
        },
      }}
      title={
        <Typography
          variant={variant}
          sx={{
            color: theme.palette.common.white,
            border: 'none',
            whiteSpace: 'normal',
            fontSize: fontSize,
            fontFamily: "Roboto",
          }}
        >
          {content}
        </Typography>
      }
    >
      {renderChildren()}
    </Tooltip>
  );
};

export default CustomTooltip;